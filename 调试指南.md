# 超鲁棒循迹小车调试指南

## 快速测试方案

### 1. 基础功能测试
```cpp
// 在loop()函数中添加调试输出
Serial.print("Pos_Err: "); Serial.print(position_error);
Serial.print(" | Confidence: "); Serial.print(turn_direction_confidence);
Serial.print(" | Sharp_Turn: "); Serial.print(in_sharp_turn);
Serial.print(" | Lost_Count: "); Serial.println(lost_track_counter);
```

### 2. 关键参数监控
- **position_error**: 位置误差 (-4.0 ~ +4.0)
- **turn_direction_confidence**: 转弯置信度 (-2.0 ~ +2.0)
- **lost_track_counter**: 丢失计数器 (0 ~ 15)
- **in_sharp_turn**: 急转弯状态 (true/false)

## 问题诊断

### 问题1: 急弯后直角弯失败
**症状**: 急右弯后遇到右直角弯停车或转错方向
**检查点**:
1. `turn_direction_confidence` 是否为正值(右转)
2. `detectSharpTurn()` 是否正确检测到急转弯
3. 轨迹历史是否记录了右偏趋势

**调整方案**:
```cpp
// 降低急转弯检测阈值，提高敏感度
// 在detectSharpTurn()函数中修改
if(abs(track_history[idx].position_error) > 1.2) // 原来是1.5
```

### 问题2: 转弯过度或不足
**症状**: 转弯角度不合适
**调整方案**:
```cpp
// 调整虚拟误差强度
position_error = turn_direction_confidence * 1.2; // 减小倍数
// 或者
position_error = turn_direction_confidence * 2.0; // 增大倍数
```

### 问题3: 直线行驶不稳定
**症状**: 直线上左右摆动
**调整方案**:
```cpp
// 调整传感器权重，增强中间传感器
sensor_weights[2] = 1.2; sensor_weights[3] = 1.2;
sensor_weights[4] = 1.2; sensor_weights[5] = 1.2;
```

## 参数调优表

| 参数 | 默认值 | 调整范围 | 作用 |
|------|--------|----------|------|
| `integral_threshold` | 0.5 | 0.3-0.8 | 积分启动阈值 |
| `SHARP_TURN_DETECTION_TIME` | 8 | 6-12 | 急转弯检测时间 |
| `LOST_TRACK_THRESHOLD` | 5 | 3-8 | 智能恢复阈值 |
| `RECOVERY_SEARCH_TIME` | 15 | 10-20 | 强制搜索时间 |
| 急转弯检测阈值 | 1.5 | 1.0-2.0 | 误差判断标准 |

## 实际测试建议

### 测试路线设计
1. **基础测试**: 直线 → 缓弯 → 直线
2. **中级测试**: 直线 → 急弯 → 直线 → 直角弯
3. **高级测试**: 急弯 → 直角弯 → S弯 → 直角弯

### 测试步骤
1. 先在简单路线上验证基础功能
2. 逐步增加路线复杂度
3. 记录失败的具体位置和传感器状态
4. 根据失败模式调整参数

### 成功标准
- ✅ 连续10次通过急弯+直角弯组合
- ✅ 直线行驶偏差 < 1cm
- ✅ 转弯恢复时间 < 300ms
- ✅ 无误判停车情况

## 常见问题解决

### Q1: 车子在直角弯处停车
**原因**: 丢失轨迹后恢复算法失效
**解决**: 增加搜索强度或延长搜索时间

### Q2: 转弯方向错误
**原因**: 转弯置信度计算错误
**解决**: 检查轨迹历史记录和趋势分析

### Q3: 响应过慢
**原因**: 动态参数调整不够
**解决**: 增大kp倍数或减小ki倍数

### Q4: 振荡现象
**原因**: 积分项过大或参数切换突变
**解决**: 减小积分增益或平滑参数切换

## 高级调试技巧

### 1. 数据记录
在SD卡上记录关键数据用于离线分析:
```cpp
// 记录格式: 时间,传感器状态,位置误差,置信度,状态
dataFile.println(String(millis()) + "," + sensorString + "," + 
                String(position_error) + "," + String(turn_direction_confidence));
```

### 2. 可视化调试
使用串口绘图器观察参数变化趋势

### 3. 分段测试
单独测试各个算法模块的正确性

记住：调试是一个迭代过程，每次只调整一个参数，观察效果后再进行下一步调整！
