# 智能循迹小车代码重构说明

## 重构目标
1. **删除直角判断逻辑**：不再使用专门的直角转弯检测，而是通过位置环误差项统一处理所有转弯情况
2. **防止积分饱和**：在小误差时才进行积分，避免积分饱和问题
3. **保持基本参数不变**：维持原有的速度设置和控制参数

## 主要改进

### 1. 删除的功能
- ❌ 删除了 `sensorHistory` 历史状态寄存器
- ❌ 删除了 `getHistoryDeviation()` 历史偏差判断函数
- ❌ 删除了 `lostCounter` 和 `LOST_THRESHOLD` 直角转弯专用逻辑
- ❌ 删除了 `SHARP_TURN_DIFF` 直角转弯速度差参数

### 2. 新增的智能功能

#### 智能位置环PI控制器 (`smartPositionPIController`)
```cpp
// 关键改进：智能积分项 - 只在小误差时积分，防止积分饱和
if (abs(position_error) < integral_threshold) {
    position_integral += position_error;
} else {
    // 大误差时逐渐减小积分项，避免突变
    position_integral *= 0.9;
}
```

**特点：**
- 只在误差小于 `integral_threshold = 0.5` 时才进行积分
- 大误差时积分项逐渐衰减，避免积分饱和
- 保持积分限幅保护

#### 加权位置误差计算 (`calculateWeightedPositionError`)
```cpp
// 使用传感器权重和位置权重
float position_weight = (i - 3.5) * sensor_weights[i];
```

**特点：**
- 中间传感器权重更高 (1.0)，边缘传感器权重稍低 (0.8-0.9)
- 有助于处理车身不正的情况
- 提高直线行驶的稳定性

#### 统一的直角弯处理
```cpp
// 处理丢失轨迹情况 - 通过增大误差项来处理直角弯
if(position_error == 999) { // 丢失轨迹
    if(last_position_error > 0) {
        position_error = 3.0; // 强制右转
    } else if(last_position_error < 0) {
        position_error = -3.0; // 强制左转
    }
}
```

**特点：**
- 不再使用专门的直角转弯逻辑
- 通过设置大的虚拟误差值来处理直角弯
- 根据上次误差方向决定转弯方向

### 3. 保留的功能
- ✅ 保持原有的增量PID控制器不变
- ✅ 保持原有的基础速度参数 (`BASE_SPEED = 45`)
- ✅ 保持原有的终点检测和处理逻辑
- ✅ 保持原有的舵机控制和物体抓取逻辑

## 控制参数

### 位置环PI参数
- `position_kp = 30.0` - 比例增益（保持原值）
- `position_ki = 5.0` - 积分增益（保持原值）
- `position_integral_limit = 25.0` - 积分限幅（保持原值）
- `integral_threshold = 0.5` - **新增**：积分启动阈值

### 传感器权重配置
```cpp
sensor_weights[8] = {0.8, 0.9, 1.0, 1.0, 1.0, 1.0, 0.9, 0.8}
```
- 中间4个传感器权重为1.0
- 边缘传感器权重递减到0.8

## 工作原理

### 正常循迹
1. 读取8个传感器数据
2. 计算加权位置误差
3. 使用智能PI控制器计算差速
4. 应用到左右轮目标速度

### 直角弯处理
1. 检测到丢失轨迹（所有传感器都为0）
2. 根据上次误差方向设置大的虚拟误差（±3.0）
3. PI控制器产生大的差速输出
4. 实现快速转弯直到重新找到轨迹

### 积分饱和防护
1. 只在小误差（< 0.5）时进行积分
2. 大误差时积分项逐渐衰减（乘以0.9）
3. 始终保持积分限幅保护

## 优势

1. **统一控制**：所有路况都通过同一个PI控制器处理，逻辑更简洁
2. **防积分饱和**：智能积分策略避免了积分饱和问题
3. **适应性强**：加权传感器可以更好地处理车身不正的情况
4. **参数保持**：基本控制参数保持不变，确保稳定性
5. **代码简化**：删除了复杂的直角转弯判断逻辑，代码更易维护

## 调试建议

如果需要调整性能，可以修改以下参数：
- `integral_threshold`：调整积分启动阈值
- `sensor_weights`：调整传感器权重分布
- 虚拟误差值（当前为±3.0）：调整直角弯转弯强度
