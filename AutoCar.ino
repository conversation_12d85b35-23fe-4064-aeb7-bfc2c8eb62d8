//加寄存器 加夹爪

#include <MsTimer2.h>
#include <Servo.h> // 引入舵机库

//舵机
Servo servoShoulder;  // 肩关节舵机（水平旋转）
Servo servoElbow;     // 肘关节舵机（垂直摆动）
Servo servoGripper;   // 手爪舵机
// 角度范围
int Max = 180, Min = 0;

// 红外传感器引脚定义
const int SENSOR1 = A0;
const int SENSOR2 = A1;
const int SENSOR3 = A2;
const int SENSOR4 = A3;
const int SENSOR5 = A4;
const int SENSOR6 = A5;
const int SENSOR7 = A6;
const int SENSOR8 = A7;

// 电机控制引脚
#define ENCODER_A_L  2
#define ENCODER_B_L  4
#define ENCODER_A_R  3
#define ENCODER_B_R  5
#define PWML        11
#define PWMR        12
#define DIR_LEFT    6
#define DIR_RIGHT   7

// 控制参数
#define PERIOD      20
#define BASE_SPEED  45
#define MAX_SPEED   80
#define MIN_SPEED   10
#define MAX_PWM     255

// [新增] 直角转弯参数
#define HISTORY_SIZE    4     // 历史状态记录深度
#define LOST_THRESHOLD  30    // 丢失轨迹阈值(约600ms)
#define SHARP_TURN_DIFF 40   // 直角转弯速度差

#define ROTATE_SPEED 25  // 旋转时的较低速度

// 全局变量
volatile float TARGET_L = 0;
volatile float TARGET_R = 0;
volatile float encoderVal_L = 0;
volatile float encoderVal_R = 0;
volatile float velocity_L = 0;
volatile float velocity_R = 0;
volatile float uL = 0;
volatile float uR = 0;

// 增量PID参数 (换算后的标准kp, ki, kd形式)
float kp_L = 5, ki_L = 0, kd_L = 0.1;
float kp_R = 5, ki_R = 0, kd_R = 0.1;

// 误差历史值
volatile float LeI = 0, LeII = 0, LeIII = 0;
volatile float ReI = 0, ReII = 0, ReIII = 0;
volatile int Loutput = 0, Routput = 0;

int pwm_L = 0;
int pwm_R = 0;
// 注意：sumI_L 和 SumI_R 变量在增量PID中不再需要，已删除

// 循迹相关变量
int sensorState[8] = {1, 1, 1, 1, 1, 1, 1, 1};
int lastTrackState = 0;
unsigned long lostTrackTime = 0;

// [新增] 直角转弯相关变量
int sensorHistory[HISTORY_SIZE][8]; // 传感器历史状态寄存器
int historyIndex = 0;               // 当前历史记录位置
int lostCounter = 0;                // 丢失轨迹计数器
int lastDeviation = 0;              // 上次的偏差方向(左:-1, 右:1)

// [新增] 位置环PI控制器参数
float position_kp = 30.0;         // 位置环比例增益
float position_ki = 5;          // 位置环积分增益
float position_integral = 0;      // 位置环积分累积值
float position_integral_limit = 25.0;  // 积分限幅

// [新增] 终点处理状态机变量
enum EndpointState {
  NORMAL_TRACKING,    // 正常循迹
  ENDPOINT_STOP,      // 终点停车
  ENDPOINT_ROTATE,    // 终点旋转
  ENDPOINT_RELEASE,   // 释放物体
  ENDPOINT_FINISHED   // 任务完成
};

EndpointState currentState = NORMAL_TRACKING;
unsigned long stateStartTime = 0;
unsigned long rotateStartTime = 0;

// [新增] 鲁棒终点检测变量
int endpointDetectionCount = 0;      // 连续检测到终点的次数
#define ENDPOINT_DETECTION_THRESHOLD 10  // 需要连续检测10次(200ms)才确认终点
#define MIN_BLACK_SENSORS 6              // 至少6个传感器检测到黑线才算可能的终点

// 编码器中断处理函数
void getEncoder_L() {
  if (digitalRead(ENCODER_A_L) == LOW) {
    encoderVal_L += (digitalRead(ENCODER_B_L) == LOW) ? -1 : 1;
  } else {
    encoderVal_L += (digitalRead(ENCODER_B_L) == LOW) ? 1 : -1;
  }
}

void getEncoder_R() {
  if (digitalRead(ENCODER_A_R) == LOW) {
    encoderVal_R += (digitalRead(ENCODER_B_R) == LOW) ? 1 : -1;
  } else {
    encoderVal_R += (digitalRead(ENCODER_B_R) == LOW) ? -1 : 1;
  }
}

// 增量PID控制算法
int pidcontrol_L(float target, float current) {
  LeI = target - current;  // 当前误差 e(k)

  // 增量PID公式: Δu(k) = kp*[e(k)-e(k-1)] + ki*e(k) + kd*[e(k)-2*e(k-1)+e(k-2)]
  float deltaU = kp_L * (LeI - LeII) + ki_L * LeI + kd_L * (LeI - 2*LeII + LeIII);

  // 累积输出: u(k) = u(k-1) + Δu(k)
  uL += deltaU;

  // 更新误差历史
  LeIII = LeII;  // e(k-2) = e(k-1)
  LeII = LeI;    // e(k-1) = e(k)

  // 输出限幅
  if (uL > MAX_PWM) uL = MAX_PWM;
  else if (uL < -MAX_PWM) uL = -MAX_PWM;

  Loutput = uL;
  return (int)Loutput;
}

int pidcontrol_R(float target, float current) {
  ReI = target - current;  // 当前误差 e(k)

  // 增量PID公式: Δu(k) = kp*[e(k)-e(k-1)] + ki*e(k) + kd*[e(k)-2*e(k-1)+e(k-2)]
  float deltaU = kp_R * (ReI - ReII) + ki_R * ReI + kd_R * (ReI - 2*ReII + ReIII);

  // 累积输出: u(k) = u(k-1) + Δu(k)
  uR += deltaU;

  // 更新误差历史
  ReIII = ReII;  // e(k-2) = e(k-1)
  ReII = ReI;    // e(k-1) = e(k)

  // 输出限幅
  if (uR > MAX_PWM) uR = MAX_PWM;
  else if (uR < -MAX_PWM) uR = -MAX_PWM;

  Routput = uR;
  return (int)Routput;
}

// [修改] 读取传感器并更新历史状态
void readSensors() {
  sensorState[0] = digitalRead(SENSOR1);
  sensorState[1] = digitalRead(SENSOR2);
  sensorState[2] = digitalRead(SENSOR3);
  sensorState[3] = digitalRead(SENSOR4);
  sensorState[4] = digitalRead(SENSOR5);
  sensorState[5] = digitalRead(SENSOR6);
  sensorState[6] = digitalRead(SENSOR7);
  sensorState[7] = digitalRead(SENSOR8);
  
  // 更新历史寄存器
  for(int i=0; i<8; i++) {
    sensorHistory[historyIndex][i] = sensorState[i];
  }
  historyIndex = (historyIndex + 1) % HISTORY_SIZE;
}

// [新增] 获取历史主要偏差方向
int getHistoryDeviation() {
  int leftCount = 0;
  int rightCount = 0;

  for(int i=0; i<HISTORY_SIZE; i++) {
    int sensorSum = 0;
    int activeSensors = 0;

    for(int j=0; j<8; j++) {
      if(sensorHistory[i][j] == 1) {
        sensorSum += (j - 3.5);
        activeSensors++;
      }
    }

    if(activeSensors > 0) {
      float deviation = sensorSum / (float)activeSensors;
      if(deviation > 0) rightCount++;
      else if(deviation < 0) leftCount++;
    }
  }

  if(leftCount > rightCount) return -1;
  else if(rightCount > leftCount) return 1;
  else return 0;
}

// [新增] 位置环PI控制器 - 从传感器数据计算差速
float positionPIController(float position_error) {
  // PI控制器计算
  // 比例项
  float proportional = position_kp * position_error;

  // 积分项
  position_integral += position_error;
  // 积分限幅，防止积分饱和
  if (position_integral > position_integral_limit) {
    position_integral = position_integral_limit;
  } else if (position_integral < -position_integral_limit) {
    position_integral = -position_integral_limit;
  }
  float integral = position_ki * position_integral;

  // PI输出 = 比例项 + 积分项
  float output = proportional + integral;

  return output;
}

// [新增] 设置位置环PI参数的便捷函数
void setPositionPIParams(float kp, float ki, float integral_limit) {
  position_kp = constrain(kp, 0.1, 100.0);
  position_ki = constrain(ki, 0.0, 10.0);
  position_integral_limit = constrain(integral_limit, 10.0, 200.0);
}

// 释放物体流程：移动至目标位置 → 张开手爪
 void putDownObject() {
  setPositions(30, 75, 0); //肩水平
  delay(1000);
  setPositions(130, 75, 0); //抓夹打开
  delay(1000);
  setPositions(130, 75, 180); //肩抬起
  delay(1000);
 }

// [新增] 终点处理状态机
void handleEndpointSequence() {
  unsigned long currentTime = millis();

  switch(currentState) {
    case ENDPOINT_STOP:
      // 停车阶段
      TARGET_L = 0;
      TARGET_R = 0;
      if (currentTime - stateStartTime >= 500) { // 停车500ms
        currentState = ENDPOINT_ROTATE;
        rotateStartTime = currentTime;
      }
      break;

    case ENDPOINT_ROTATE:
      // 旋转阶段 - 使用较低速度原地旋转
      TARGET_L = ROTATE_SPEED;   // 左轮正转
      TARGET_R = -ROTATE_SPEED;  // 右轮反转
      if (currentTime - rotateStartTime >=1500) { // 旋转2秒，您可以调整这个时间
        currentState = ENDPOINT_RELEASE;
        stateStartTime = currentTime;
        TARGET_L = 0;
        TARGET_R = 0;
      }
      break;

    case ENDPOINT_RELEASE:
      // 释放物体阶段
      TARGET_L = 0;
      TARGET_R = 0;
      // 等待足够时间让主循环执行舵机动作（约4秒）
      if (currentTime - stateStartTime >= 4000) {
        currentState = ENDPOINT_FINISHED;
      }
      break;

    case ENDPOINT_FINISHED:
      // 任务完成
      TARGET_L = 0;
      TARGET_R = 0;
      break;

    default:
      break;
  }
}

// [修改] 计算目标速度（增加直角转弯处理）
void calculateTargetSpeed() {
  int sensorSum = 0;
  int activeSensors = 0;

  for(int i=0; i<8; i++) {
    if(sensorState[i] == 1) {
      sensorSum += (i - 3.5);
      activeSensors++;
    }
  }

  // [新增] 鲁棒的终点检测逻辑
  if (currentState == NORMAL_TRACKING) {
    // 检查是否有足够多的传感器检测到黑线（可能的终点）
    if (activeSensors >= MIN_BLACK_SENSORS) {
      endpointDetectionCount++;
      // 连续检测到足够次数才确认为终点
      if (endpointDetectionCount >= ENDPOINT_DETECTION_THRESHOLD) {
        currentState = ENDPOINT_STOP;
        stateStartTime = millis();
        endpointDetectionCount = 0;  // 重置计数器
        return;
      }
    } else {
      // 如果检测不到大面积黑线，重置计数器
      endpointDetectionCount = 0;
    }
  }

  // 如果正在执行终点处理序列，调用状态机
  if (currentState != NORMAL_TRACKING) {
    handleEndpointSequence();
    return;
  }
  
  // 处理丢失轨迹情况（直角转弯时）
  if(activeSensors == 0) {
    lostCounter++;
    
    // 根据历史状态判断转弯方向
    int histDev = getHistoryDeviation();
    if(histDev != 0) {
      lastDeviation = histDev;
    }
    
    // 根据最后已知方向进行转弯
    if(lostCounter < LOST_THRESHOLD) {
      if(lastDeviation < 0) { // 最后是左偏
        TARGET_L = BASE_SPEED - SHARP_TURN_DIFF;
        TARGET_R = BASE_SPEED + SHARP_TURN_DIFF;
      } else { // 最后是右偏
        TARGET_L = BASE_SPEED + SHARP_TURN_DIFF;
        TARGET_R = BASE_SPEED - SHARP_TURN_DIFF;
      }
    } else {
      TARGET_L = 0;
      TARGET_R = 0;
    }
    return;
  }
  
  // 重置丢失计数器
  lostCounter = 0;
  lastTrackState = 1;

  // [重构] 计算位置误差 (目标位置为0，即中心线)
  float position_error = sensorSum / (float)activeSensors;
  lastDeviation = (position_error > 0) ? 1 : ((position_error < 0) ? -1 : 0);

  // [新增] 使用位置环PI控制器计算差速
  float speedDiff = positionPIController(position_error);

  // 差速限幅，确保不超过合理范围
  speedDiff = constrain(speedDiff, -(MAX_SPEED - BASE_SPEED), (MAX_SPEED - BASE_SPEED));

  TARGET_L = BASE_SPEED - speedDiff;
  TARGET_R = BASE_SPEED + speedDiff;
  
  // 速度限幅
  TARGET_L = constrain(TARGET_L, MIN_SPEED, MAX_SPEED);
  TARGET_R = constrain(TARGET_R, MIN_SPEED, MAX_SPEED);
}

void controlMotors() {
  velocity_L = (encoderVal_L / 780.0) * 3.1415 * 2.0 * (1000.0 / PERIOD) * 10;
  velocity_R = (encoderVal_R / 780.0) * 3.1415 * 2.0 * (1000.0 / PERIOD) * 10;
  
  pwm_L = pidcontrol_L(TARGET_L, velocity_L);
  pwm_R = pidcontrol_R(TARGET_R, velocity_R);
  
  // 控制左电机
  if (pwm_L >= 0) {
    digitalWrite(DIR_LEFT, HIGH);
  } else {
    digitalWrite(DIR_LEFT, LOW);
  }
  analogWrite(PWML, abs(pwm_L));
  
  // 控制右电机
  if (pwm_R >= 0) {
    digitalWrite(DIR_RIGHT, LOW);
  } else {
    digitalWrite(DIR_RIGHT, HIGH);
  }
  analogWrite(PWMR, abs(pwm_R));
  
  encoderVal_L = 0;
  encoderVal_R = 0;
}

void controlTask() {
  readSensors();
  calculateTargetSpeed();
  controlMotors();
}

//舵机
void setServoAngle(Servo &servo, int angle, int min, int max) {
  // 角度限制
  angle = constrain(angle, min, max);
  servo.write(angle);
  delay(15); // 舵机转动延迟（每1°约需15ms）
}

// 批量设置所有舵机位置
void setPositions(int shoulder, int elbow, int gripper) {
  setServoAngle(servoShoulder, shoulder, Min, Max);
  setServoAngle(servoElbow, elbow, Min, Max);
  setServoAngle(servoGripper, gripper, Min, Max);
  delay(100); // 等待动作完成
}

void setup() {
  //舵机
  
  // 绑定舵机引脚
  servoShoulder.attach(8);
  servoElbow.attach(9);
  servoGripper.attach(10);

  // 校准初始位置
  //抓取物体
  setPositions(130, 75, 180); //抓夹打开水平
  delay(500);
  setPositions(130, 75, 0); //抓夹闭合
  delay(500);
  setPositions(30, 75, 0); //肩抬起  
  delay(500);

  // 配置PWM频率
  TCCR1B = TCCR1B & B11111000 | B00000001;
  
  // 初始化引脚
  pinMode(PWML, OUTPUT);
  pinMode(PWMR, OUTPUT);
  pinMode(DIR_LEFT, OUTPUT);
  pinMode(DIR_RIGHT, OUTPUT);
  pinMode(SENSOR1, INPUT);
  pinMode(SENSOR2, INPUT);
  pinMode(SENSOR3, INPUT);
  pinMode(SENSOR4, INPUT);
  pinMode(SENSOR5, INPUT);
  pinMode(SENSOR6, INPUT);
  pinMode(SENSOR7, INPUT);
  pinMode(SENSOR8, INPUT);
  pinMode(ENCODER_A_L, INPUT);
  pinMode(ENCODER_B_L, INPUT);
  pinMode(ENCODER_A_R, INPUT);
  pinMode(ENCODER_B_R, INPUT);
  
  // 配置中断
  attachInterrupt(0, getEncoder_L, CHANGE);
  attachInterrupt(1, getEncoder_R, CHANGE);
  
  // 初始化串口
  Serial.begin(9600);
  
  // [新增] 初始化历史寄存器
  for(int i=0; i<HISTORY_SIZE; i++) {
    for(int j=0; j<8; j++) {
      sensorHistory[i][j] = 1;
    }
  }
  
  // 启动定时器
  MsTimer2::set(PERIOD, controlTask);
  MsTimer2::start();
  
  digitalWrite(DIR_LEFT, LOW);
  digitalWrite(DIR_RIGHT, LOW);
}

void loop() {
  // 处理终点释放物体动作（在主循环中执行，因为舵机函数有delay）
  static bool releaseExecuted = false;
  static unsigned long lastReleaseTime = 0;

  if (currentState == ENDPOINT_RELEASE && !releaseExecuted) {
    // 确保在进入ENDPOINT_RELEASE状态后立即执行一次
    putDownObject();  // 执行释放物体动作
    releaseExecuted = true;
    lastReleaseTime = millis();
    Serial.println("物体已释放！");  // 调试信息
  }

  // 重置释放标志（当重新开始循迹时）
  if (currentState == NORMAL_TRACKING) {
    releaseExecuted = false;
  }

  // 调试信息 - 可以取消注释来观察状态
  Serial.print("State: ");
  Serial.print(currentState);
  Serial.print(" | EndpointCount: ");
  Serial.print(endpointDetectionCount);
  Serial.print(" | Sensors: ");
  for(int i=0; i<8; i++) {
    Serial.print(sensorState[i]);
  }
  Serial.print(" | Target: L=");
  Serial.print(TARGET_L);
  Serial.print(", R=");
  Serial.print(TARGET_R);
  Serial.println("");

  delay(200);
}
