//重构版本 - 智能循迹小车控制系统

#include <MsTimer2.h>
#include <Servo.h> // 引入舵机库

//舵机
Servo servoShoulder;  // 肩关节舵机（水平旋转）
Servo servoElbow;     // 肘关节舵机（垂直摆动）
Servo servoGripper;   // 手爪舵机
// 角度范围
int Max = 180, Min = 0;

// 红外传感器引脚定义
const int SENSOR1 = A0;
const int SENSOR2 = A1;
const int SENSOR3 = A2;
const int SENSOR4 = A3;
const int SENSOR5 = A4;
const int SENSOR6 = A5;
const int SENSOR7 = A6;
const int SENSOR8 = A7;

// 电机控制引脚
#define ENCODER_A_L  2
#define ENCODER_B_L  4
#define ENCODER_A_R  3
#define ENCODER_B_R  5
#define PWML        11
#define PWMR        12
#define DIR_LEFT    6
#define DIR_RIGHT   7

// 控制参数
#define PERIOD      20
#define BASE_SPEED  45
#define MAX_SPEED   80
#define MIN_SPEED   10
#define MAX_PWM     255

#define ROTATE_SPEED 25  // 旋转时的较低速度

// 全局变量
volatile float TARGET_L = 0;
volatile float TARGET_R = 0;
volatile float encoderVal_L = 0;
volatile float encoderVal_R = 0;
volatile float velocity_L = 0;
volatile float velocity_R = 0;
volatile float uL = 0;
volatile float uR = 0;

// 增量PID参数 (换算后的标准kp, ki, kd形式)
float kp_L = 5, ki_L = 0, kd_L = 0.1;
float kp_R = 5, ki_R = 0, kd_R = 0.1;

// 误差历史值
volatile float LeI = 0, LeII = 0, LeIII = 0;
volatile float ReI = 0, ReII = 0, ReIII = 0;
volatile int Loutput = 0, Routput = 0;

int pwm_L = 0;
int pwm_R = 0;

// 循迹相关变量
int sensorState[8] = {1, 1, 1, 1, 1, 1, 1, 1};

// [增强] 鲁棒位置环PI控制器参数
float position_kp = 30.0;           // 位置环比例增益
float position_ki = 1.0;            // 位置环积分增益
float position_integral = 0;        // 位置环积分累积值
float position_integral_limit = 15.0; // 积分限幅
float integral_threshold = 1.5;     // 积分启动阈值，小误差时才积分
float last_position_error = 0;      // 上次位置误差，用于微分计算

// [增强] 传感器权重配置 - 用于处理复杂路况
float sensor_weights[8] = {1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0}; // 中间传感器权重更高

// [优化] 鲁棒轨迹跟踪增强参数
#define TRACK_HISTORY_SIZE 10        // 轨迹历史记录深度
#define LOST_TRACK_THRESHOLD 8       // 丢失轨迹判断阈值(增加到160ms)
#define RECOVERY_SEARCH_TIME 25      // 恢复搜索时间(增加到500ms)
#define SHARP_TURN_DETECTION_TIME 8  // 急转弯检测时间(约160ms)
#define MAX_CONTINUOUS_TURNS 3       // 最大连续转弯次数

// 轨迹状态历史记录
struct TrackState {
  float position_error;
  int active_sensors;
  unsigned long timestamp;
};

TrackState track_history[TRACK_HISTORY_SIZE];
int history_index = 0;
int lost_track_counter = 0;
bool in_sharp_turn = false;
unsigned long sharp_turn_start_time = 0;
float turn_direction_confidence = 0; // 转弯方向置信度 (-1左, +1右)

// [新增] 连续转弯处理变量
int continuous_turn_count = 0;       // 连续转弯计数
unsigned long last_track_found_time = 0; // 上次找到轨迹的时间
bool recently_found_track = false;   // 最近是否找到过轨迹
unsigned long turn_sequence_start = 0; // 转弯序列开始时间

// [保留] 终点处理状态机变量
enum EndpointState {
  NORMAL_TRACKING,    // 正常循迹
  ENDPOINT_STOP,      // 终点停车
  ENDPOINT_ROTATE,    // 终点旋转
  ENDPOINT_RELEASE,   // 释放物体
  ENDPOINT_FINISHED   // 任务完成
};

EndpointState currentState = NORMAL_TRACKING;
unsigned long stateStartTime = 0;
unsigned long rotateStartTime = 0;

// [保留] 鲁棒终点检测变量
int endpointDetectionCount = 0;      // 连续检测到终点的次数
#define ENDPOINT_DETECTION_THRESHOLD 10  // 需要连续检测10次(200ms)才确认终点
#define MIN_BLACK_SENSORS 6              // 至少6个传感器检测到黑线才算可能的终点

// 编码器中断处理函数
void getEncoder_L() {
  if (digitalRead(ENCODER_A_L) == LOW) {
    encoderVal_L += (digitalRead(ENCODER_B_L) == LOW) ? -1 : 1;
  } else {
    encoderVal_L += (digitalRead(ENCODER_B_L) == LOW) ? 1 : -1;
  }
}

void getEncoder_R() {
  if (digitalRead(ENCODER_A_R) == LOW) {
    encoderVal_R += (digitalRead(ENCODER_B_R) == LOW) ? 1 : -1;
  } else {
    encoderVal_R += (digitalRead(ENCODER_B_R) == LOW) ? -1 : 1;
  }
}

// 增量PID控制算法
int pidcontrol_L(float target, float current) {
  LeI = target - current;  // 当前误差 e(k)

  // 增量PID公式: Δu(k) = kp*[e(k)-e(k-1)] + ki*e(k) + kd*[e(k)-2*e(k-1)+e(k-2)]
  float deltaU = kp_L * (LeI - LeII) + ki_L * LeI + kd_L * (LeI - 2*LeII + LeIII);

  // 累积输出: u(k) = u(k-1) + Δu(k)
  uL += deltaU;

  // 更新误差历史
  LeIII = LeII;  // e(k-2) = e(k-1)
  LeII = LeI;    // e(k-1) = e(k)

  // 输出限幅
  if (uL > MAX_PWM) uL = MAX_PWM;
  else if (uL < -MAX_PWM) uL = -MAX_PWM;

  Loutput = uL;
  return (int)Loutput;
}

int pidcontrol_R(float target, float current) {
  ReI = target - current;  // 当前误差 e(k)

  // 增量PID公式: Δu(k) = kp*[e(k)-e(k-1)] + ki*e(k) + kd*[e(k)-2*e(k-1)+e(k-2)]
  float deltaU = kp_R * (ReI - ReII) + ki_R * ReI + kd_R * (ReI - 2*ReII + ReIII);

  // 累积输出: u(k) = u(k-1) + Δu(k)
  uR += deltaU;

  // 更新误差历史
  ReIII = ReII;  // e(k-2) = e(k-1)
  ReII = ReI;    // e(k-1) = e(k)

  // 输出限幅
  if (uR > MAX_PWM) uR = MAX_PWM;
  else if (uR < -MAX_PWM) uR = -MAX_PWM;

  Routput = uR;
  return (int)Routput;
}

// [增强] 读取传感器数据并更新历史记录
void readSensors() {
  sensorState[0] = digitalRead(SENSOR1);
  sensorState[1] = digitalRead(SENSOR2);
  sensorState[2] = digitalRead(SENSOR3);
  sensorState[3] = digitalRead(SENSOR4);
  sensorState[4] = digitalRead(SENSOR5);
  sensorState[5] = digitalRead(SENSOR6);
  sensorState[6] = digitalRead(SENSOR7);
  sensorState[7] = digitalRead(SENSOR8);

  // 更新轨迹历史记录
  updateTrackHistory();
}

// [新增] 更新轨迹历史记录
void updateTrackHistory() {
  int active_sensors = 0;
  float weighted_sum = 0;
  float total_weight = 0;

  for(int i = 0; i < 8; i++) {
    if(sensorState[i] == 1) {
      float position_weight = (i - 3.5) * sensor_weights[i];
      weighted_sum += position_weight;
      total_weight += sensor_weights[i];
      active_sensors++;
    }
  }

  // 记录当前状态
  track_history[history_index].active_sensors = active_sensors;
  track_history[history_index].timestamp = millis();

  if(active_sensors > 0) {
    track_history[history_index].position_error = weighted_sum / total_weight;
  } else {
    track_history[history_index].position_error = 999; // 丢失轨迹标记
  }

  history_index = (history_index + 1) % TRACK_HISTORY_SIZE;
}

// [重构] 智能位置环PI控制器 - 防积分饱和，处理复杂路况
float smartPositionPIController(float position_error) {
  // 比例项
  float proportional = position_kp * position_error;

  // [关键改进] 智能积分项 - 只在小误差时积分，防止积分饱和
  if (abs(position_error) < integral_threshold) {
    position_integral += position_error;
  } else {
    // 大误差时逐渐减小积分项，避免突变
    position_integral *= 0.9;
  }

  // 积分限幅，防止积分饱和
  if (position_integral > position_integral_limit) {
    position_integral = position_integral_limit;
  } else if (position_integral < -position_integral_limit) {
    position_integral = -position_integral_limit;
  }
  float integral = position_ki * position_integral;

  // PI输出 = 比例项 + 积分项
  float output = proportional + integral;

  // 更新历史误差
  last_position_error = position_error;

  return output;
}

// [新增] 分析轨迹历史，判断转弯趋势
float analyzeTrackTrend() {
  float trend_sum = 0;
  int valid_samples = 0;
  unsigned long current_time = millis();

  // 分析最近的轨迹历史
  for(int i = 0; i < TRACK_HISTORY_SIZE; i++) {
    int idx = (history_index - 1 - i + TRACK_HISTORY_SIZE) % TRACK_HISTORY_SIZE;

    // 只分析最近200ms内的数据
    if(current_time - track_history[idx].timestamp < 200 &&
       track_history[idx].position_error != 999) {
      trend_sum += track_history[idx].position_error;
      valid_samples++;
    }
  }

  if(valid_samples > 2) {
    return trend_sum / valid_samples; // 返回平均趋势
  }
  return 0;
}

// [新增] 检测急转弯状态
bool detectSharpTurn() {
  unsigned long current_time = millis();
  int large_error_count = 0;

  // 检查最近是否有大的位置误差
  for(int i = 0; i < SHARP_TURN_DETECTION_TIME && i < TRACK_HISTORY_SIZE; i++) {
    int idx = (history_index - 1 - i + TRACK_HISTORY_SIZE) % TRACK_HISTORY_SIZE;

    if(current_time - track_history[idx].timestamp < 160 &&
       abs(track_history[idx].position_error) > 1.5) {
      large_error_count++;
    }
  }

  return large_error_count >= 4; // 如果最近有4次大误差，认为是急转弯
}

// [优化] 智能轨迹恢复算法 - 增强连续转弯处理
float smartTrackRecovery() {
  // 分析轨迹趋势
  float trend = analyzeTrackTrend();

  // 检测是否刚经历急转弯
  bool just_sharp_turn = detectSharpTurn();

  // 检查是否在连续转弯序列中
  unsigned long current_time = millis();
  bool in_turn_sequence = (current_time - turn_sequence_start < 2000); // 2秒内的转弯序列

  // 更新转弯方向置信度
  if(abs(trend) > 0.3) {
    turn_direction_confidence = turn_direction_confidence * 0.7 + trend * 0.3;
  }

  // 在连续转弯中，保持更强的方向记忆
  if(in_turn_sequence && continuous_turn_count > 0) {
    // 连续转弯时，置信度衰减更慢
    turn_direction_confidence *= 0.95; // 慢衰减
  }

  // 限制置信度范围
  turn_direction_confidence = constrain(turn_direction_confidence, -3.0, 3.0); // 扩大范围

  // 根据不同情况返回恢复策略
  if(in_turn_sequence && continuous_turn_count > 0) {
    // 连续转弯序列中，使用更强的信号
    return turn_direction_confidence * 2.0;
  } else if(just_sharp_turn) {
    // 刚经历急转弯，使用更强的恢复信号
    return turn_direction_confidence * 1.5;
  } else if(abs(turn_direction_confidence) > 0.5) {
    // 有明确的转弯趋势
    return turn_direction_confidence * 1.2;
  } else {
    // 保守恢复
    return last_position_error * 0.8;
  }
}

// [新增] 连续转弯检测和管理
void manageContinuousTurns(bool track_found) {
  unsigned long current_time = millis();

  if(track_found) {
    // 找到轨迹
    last_track_found_time = current_time;
    recently_found_track = true;

    // 重置丢失计数器
    lost_track_counter = 0;

    // 如果刚从丢失状态恢复，可能完成了一次转弯
    if(!recently_found_track) {
      continuous_turn_count++;
      if(continuous_turn_count == 1) {
        turn_sequence_start = current_time; // 开始转弯序列计时
      }
    }
  } else {
    // 丢失轨迹
    recently_found_track = false;
  }

  // 重置连续转弯计数（如果长时间没有转弯）
  if(current_time - turn_sequence_start > 3000) { // 3秒后重置
    continuous_turn_count = 0;
    turn_sequence_start = current_time;
  }

  // 限制最大连续转弯次数
  if(continuous_turn_count > MAX_CONTINUOUS_TURNS) {
    continuous_turn_count = MAX_CONTINUOUS_TURNS;
  }
}

// [保留] 设置位置环PI参数的便捷函数
void setPositionPIParams(float kp, float ki, float integral_limit) {
  position_kp = constrain(kp, 0.1, 100.0);
  position_ki = constrain(ki, 0.0, 10.0);
  position_integral_limit = constrain(integral_limit, 10.0, 200.0);
}

// 释放物体流程：移动至目标位置 → 张开手爪
 void putDownObject() {
  setPositions(30, 75, 0); //肩水平
  delay(1000);
  setPositions(130, 75, 0); //抓夹打开
  delay(1000);
  setPositions(130, 75, 180); //肩抬起
  delay(1000);
 }

// [新增] 终点处理状态机
void handleEndpointSequence() {
  unsigned long currentTime = millis();

  switch(currentState) {
    case ENDPOINT_STOP:
      // 停车阶段
      TARGET_L = 0;
      TARGET_R = 0;
      if (currentTime - stateStartTime >= 500) { // 停车500ms
        currentState = ENDPOINT_ROTATE;
        rotateStartTime = currentTime;
      }
      break;

    case ENDPOINT_ROTATE:
      // 旋转阶段 - 使用较低速度原地旋转
      TARGET_L = ROTATE_SPEED;   // 左轮正转
      TARGET_R = -ROTATE_SPEED;  // 右轮反转
      if (currentTime - rotateStartTime >=1500) { // 旋转2秒，您可以调整这个时间
        currentState = ENDPOINT_RELEASE;
        stateStartTime = currentTime;
        TARGET_L = 0;
        TARGET_R = 0;
      }
      break;

    case ENDPOINT_RELEASE:
      // 释放物体阶段
      TARGET_L = 0;
      TARGET_R = 0;
      // 等待足够时间让主循环执行舵机动作（约4秒）
      if (currentTime - stateStartTime >= 4000) {
        currentState = ENDPOINT_FINISHED;
      }
      break;

    case ENDPOINT_FINISHED:
      // 任务完成
      TARGET_L = 0;
      TARGET_R = 0;
      break;

    default:
      break;
  }
}

// [增强] 超鲁棒目标速度计算 - 处理复杂连续转弯
void calculateTargetSpeed() {
  // [保留] 鲁棒的终点检测逻辑
  if (currentState == NORMAL_TRACKING) {
    int activeSensors = 0;
    for(int i = 0; i < 8; i++) {
      if(sensorState[i] == 1) activeSensors++;
    }

    // 检查是否有足够多的传感器检测到黑线（可能的终点）
    if (activeSensors >= MIN_BLACK_SENSORS) {
      endpointDetectionCount++;
      // 连续检测到足够次数才确认为终点
      if (endpointDetectionCount >= ENDPOINT_DETECTION_THRESHOLD) {
        currentState = ENDPOINT_STOP;
        stateStartTime = millis();
        endpointDetectionCount = 0;  // 重置计数器
        return;
      }
    } else {
      // 如果检测不到大面积黑线，重置计数器
      endpointDetectionCount = 0;
    }
  }

  // 如果正在执行终点处理序列，调用状态机
  if (currentState != NORMAL_TRACKING) {
    handleEndpointSequence();
    return;
  }

  // [增强] 获取当前轨迹状态
  int current_idx = (history_index - 1 + TRACK_HISTORY_SIZE) % TRACK_HISTORY_SIZE;
  float position_error = track_history[current_idx].position_error;
  int active_sensors = track_history[current_idx].active_sensors;

  // [优化] 多层次轨迹丢失处理 - 防止连续转弯时误停车
  bool track_found = !(position_error == 999 || active_sensors == 0);

  // 管理连续转弯状态
  manageContinuousTurns(track_found);

  if(!track_found) { // 丢失轨迹
    lost_track_counter++;

    // 计算动态恢复时间限制
    int dynamic_threshold = LOST_TRACK_THRESHOLD;
    int dynamic_search_time = RECOVERY_SEARCH_TIME;

    // 在连续转弯序列中，延长恢复时间
    if(continuous_turn_count > 0) {
      dynamic_threshold += continuous_turn_count * 3;  // 每次转弯增加60ms
      dynamic_search_time += continuous_turn_count * 5; // 每次转弯增加100ms
    }

    // 第一阶段：智能恢复 (动态调整时间)
    if(lost_track_counter <= dynamic_threshold) {
      position_error = smartTrackRecovery();
    }
    // 第二阶段：强制搜索 (动态调整时间)
    else if(lost_track_counter <= dynamic_search_time) {
      // 使用更强的搜索信号
      float search_intensity = 1.0 + (lost_track_counter - dynamic_threshold) * 0.2;

      // 在连续转弯中使用更强的搜索
      if(continuous_turn_count > 0) {
        search_intensity *= (1.0 + continuous_turn_count * 0.3);
      }

      position_error = turn_direction_confidence * search_intensity * 2.5;

      // 如果置信度不够，使用最后已知方向
      if(abs(turn_direction_confidence) < 0.3) {
        position_error = (last_position_error > 0) ? 3.0 : -3.0;
      }
    }
    // 第三阶段：紧急停车 (只有在非连续转弯或超时才停车)
    else {
      // 如果在连续转弯序列中且时间不太长，继续尝试
      if(continuous_turn_count > 0 && (millis() - turn_sequence_start < 5000)) {
        // 继续使用强搜索，但减弱信号
        position_error = turn_direction_confidence * 1.5;
        if(abs(position_error) < 1.0) {
          position_error = (last_position_error > 0) ? 2.0 : -2.0;
        }
      } else {
        // 真正的紧急停车
        TARGET_L = 0;
        TARGET_R = 0;
        continuous_turn_count = 0; // 重置连续转弯计数
        return;
      }
    }
  } else {
    // 找到轨迹
    // 检测急转弯状态
    if(detectSharpTurn() && !in_sharp_turn) {
      in_sharp_turn = true;
      sharp_turn_start_time = millis();
    } else if(in_sharp_turn && (millis() - sharp_turn_start_time > 300)) {
      in_sharp_turn = false; // 急转弯状态持续300ms后解除
    }
  }

  // [增强] 动态调整控制参数
  float dynamic_kp = position_kp;
  float dynamic_ki = position_ki;

  // 在急转弯后增强响应
  if(in_sharp_turn || lost_track_counter > 0) {
    dynamic_kp *= 1.3; // 增强比例增益
    dynamic_ki *= 0.7; // 减少积分增益，避免超调
  }

  // 临时调整控制器参数
  float temp_kp = position_kp;
  float temp_ki = position_ki;
  position_kp = dynamic_kp;
  position_ki = dynamic_ki;

  // [增强] 使用智能位置环PI控制器计算差速
  float speedDiff = smartPositionPIController(position_error);

  // 恢复原始参数
  position_kp = temp_kp;
  position_ki = temp_ki;

  // 差速限幅，确保不超过合理范围
  speedDiff = constrain(speedDiff, -(MAX_SPEED - BASE_SPEED), (MAX_SPEED - BASE_SPEED));

  TARGET_L = BASE_SPEED - speedDiff;
  TARGET_R = BASE_SPEED + speedDiff;

  // 速度限幅
  TARGET_L = constrain(TARGET_L, MIN_SPEED, MAX_SPEED);
  TARGET_R = constrain(TARGET_R, MIN_SPEED, MAX_SPEED);
}

void controlMotors() {
  velocity_L = (encoderVal_L / 780.0) * 3.1415 * 2.0 * (1000.0 / PERIOD) * 10;
  velocity_R = (encoderVal_R / 780.0) * 3.1415 * 2.0 * (1000.0 / PERIOD) * 10;
  
  pwm_L = pidcontrol_L(TARGET_L, velocity_L);
  pwm_R = pidcontrol_R(TARGET_R, velocity_R);
  
  // 控制左电机
  if (pwm_L >= 0) {
    digitalWrite(DIR_LEFT, HIGH);
  } else {
    digitalWrite(DIR_LEFT, LOW);
  }
  analogWrite(PWML, abs(pwm_L));
  
  // 控制右电机
  if (pwm_R >= 0) {
    digitalWrite(DIR_RIGHT, LOW);
  } else {
    digitalWrite(DIR_RIGHT, HIGH);
  }
  analogWrite(PWMR, abs(pwm_R));
  
  encoderVal_L = 0;
  encoderVal_R = 0;
}

void controlTask() {
  readSensors();
  calculateTargetSpeed();
  controlMotors();
}

//舵机
void setServoAngle(Servo &servo, int angle, int min, int max) {
  // 角度限制
  angle = constrain(angle, min, max);
  servo.write(angle);
  delay(15); // 舵机转动延迟（每1°约需15ms）
}

// 批量设置所有舵机位置
void setPositions(int shoulder, int elbow, int gripper) {
  setServoAngle(servoShoulder, shoulder, Min, Max);
  setServoAngle(servoElbow, elbow, Min, Max);
  setServoAngle(servoGripper, gripper, Min, Max);
  delay(100); // 等待动作完成
}

void setup() {
  //舵机
  
  // 绑定舵机引脚
  servoShoulder.attach(8);
  servoElbow.attach(9);
  servoGripper.attach(10);

  // 校准初始位置
  //抓取物体
  setPositions(130, 75, 180); //抓夹打开水平
  delay(500);
  setPositions(130, 75, 0); //抓夹闭合
  delay(500);
  setPositions(30, 75, 0); //肩抬起  
  delay(500);

  // 配置PWM频率
  TCCR1B = TCCR1B & B11111000 | B00000001;
  
  // 初始化引脚
  pinMode(PWML, OUTPUT);
  pinMode(PWMR, OUTPUT);
  pinMode(DIR_LEFT, OUTPUT);
  pinMode(DIR_RIGHT, OUTPUT);
  pinMode(SENSOR1, INPUT);
  pinMode(SENSOR2, INPUT);
  pinMode(SENSOR3, INPUT);
  pinMode(SENSOR4, INPUT);
  pinMode(SENSOR5, INPUT);
  pinMode(SENSOR6, INPUT);
  pinMode(SENSOR7, INPUT);
  pinMode(SENSOR8, INPUT);
  pinMode(ENCODER_A_L, INPUT);
  pinMode(ENCODER_B_L, INPUT);
  pinMode(ENCODER_A_R, INPUT);
  pinMode(ENCODER_B_R, INPUT);
  
  // 配置中断
  attachInterrupt(0, getEncoder_L, CHANGE);
  attachInterrupt(1, getEncoder_R, CHANGE);
  
  // 初始化串口
  Serial.begin(9600);
  
  // [增强] 初始化传感器权重（可根据实际情况调整）
  // 中间传感器权重更高，边缘传感器权重稍低，有助于稳定循迹
  sensor_weights[0] = 0.8; sensor_weights[1] = 0.9;
  sensor_weights[2] = 1.0; sensor_weights[3] = 1.0;
  sensor_weights[4] = 1.0; sensor_weights[5] = 1.0;
  sensor_weights[6] = 0.9; sensor_weights[7] = 0.8;

  // [新增] 初始化轨迹历史记录
  for(int i = 0; i < TRACK_HISTORY_SIZE; i++) {
    track_history[i].position_error = 0;
    track_history[i].active_sensors = 8;
    track_history[i].timestamp = millis();
  }
  
  // 启动定时器
  MsTimer2::set(PERIOD, controlTask);
  MsTimer2::start();
  
  digitalWrite(DIR_LEFT, LOW);
  digitalWrite(DIR_RIGHT, LOW);
}

void loop() {
  // 处理终点释放物体动作（在主循环中执行，因为舵机函数有delay）
  static bool releaseExecuted = false;
  static unsigned long lastReleaseTime = 0;

  if (currentState == ENDPOINT_RELEASE && !releaseExecuted) {
    // 确保在进入ENDPOINT_RELEASE状态后立即执行一次
    putDownObject();  // 执行释放物体动作
    releaseExecuted = true;
    lastReleaseTime = millis();
    Serial.println("物体已释放！");  // 调试信息
  }

  // 重置释放标志（当重新开始循迹时）
  if (currentState == NORMAL_TRACKING) {
    releaseExecuted = false;
  }

  // [增强] 调试信息 - 包含连续转弯状态
  Serial.print("State: ");
  Serial.print(currentState);
  Serial.print(" | EndpointCount: ");
  Serial.print(endpointDetectionCount);
  Serial.print(" | Sensors: ");
  for(int i=0; i<8; i++) {
    Serial.print(sensorState[i]);
  }
  Serial.print(" | Target: L=");
  Serial.print(TARGET_L);
  Serial.print(", R=");
  Serial.print(TARGET_R);
  Serial.print(" | LostCount: ");
  Serial.print(lost_track_counter);
  Serial.print(" | ContTurns: ");
  Serial.print(continuous_turn_count);
  Serial.print(" | Confidence: ");
  Serial.print(turn_direction_confidence);
  Serial.println("");

  delay(200);
}
